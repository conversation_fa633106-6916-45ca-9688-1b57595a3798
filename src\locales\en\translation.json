{"common": {"loading": "Just a moment... ⏳", "error": "😕 Oops, something went wrong", "success": "🎉 Great job!", "cancel": "❌ Cancel", "confirm": "✅ Confirm", "save": "💾 Save", "edit": "✏️ Edit", "delete": "🗑️ Delete", "back": "⬅️ Back", "next": "➡️ Next", "skip": "⏭️ Skip", "done": "✅ Done", "retry": "🔄 Try Again", "close": "❌ Close", "ok": "👍 Got it", "yes": "✅ Yes", "no": "❌ No"}, "auth": {"welcome": "Welcome to PostureApp! 🧘‍♀️", "subtitle": "Transform your posture with AI-guided wellness", "login": "🔐 Sign In", "register": "✨ Create Account", "email": "📧 Email Address", "password": "🔒 Password", "confirmPassword": "🔒 Confirm Password", "name": "👤 Full Name", "phone": "📱 Phone Number", "forgotPassword": "🔑 Forgot Password?", "loginWithGoogle": "🚀 Continue with Google", "alreadyHaveAccount": "Already have an account? 👋", "dontHaveAccount": "New here? Let's get started! ✨", "resetPassword": "🔑 Reset Password", "resetPasswordDesc": "Enter your email and we'll send you reset instructions 📧", "resetPasswordSent": "Check your email for reset instructions! 📧", "loginSuccess": "Welcome back! 👋", "registerSuccess": "Account created successfully! 🎉", "logout": "👋 Sign Out", "logoutConfirm": "You'll need to sign in again next time. Is that okay?", "loginRequired": "Please sign in to continue 🔐"}, "home": {"greeting": "Good {{timeOfDay}}, {{name}}!", "postureScore": "Today's Posture Score", "quickCheck": "Quick Posture Check", "startYoga": "Start Yoga Session", "viewProgress": "View Progress", "dailyGoal": "Daily Goal", "streak": "{{count}} day streak", "recentActivity": "Recent Activity", "noActivity": "No recent activity", "tips": "Daily Tips", "tipOfDay": "Keep your screen at eye level to avoid neck strain"}, "postureCheck": {"title": "📷 Posture Check", "instructions": "Position yourself comfortably in front of the camera 📱", "standStraight": "Stand naturally and look at the camera - you've got this! 💪", "analyzing": "Analyzing your posture... almost done! ⏳", "results": "📊 Your Results", "score": "Your Score: {{score}}/100 🎯", "excellent": "🌟 Excellent posture! Keep it up!", "good": "👍 Good posture with room for small improvements", "fair": "⚠️ Your posture needs some attention - let's work on it!", "poor": "🚨 Your posture needs immediate care - but don't worry, we'll help!", "issues": "🔍 Areas to Improve", "recommendations": "💡 Your Personal Plan", "startCorrection": "🎯 Fix My Posture", "saveResults": "💾 Save Results", "retake": "📸 Try Again"}, "yoga": {"title": "Yoga Exercises", "recommended": "Recommended for You", "allPoses": "All Poses", "beginner": "<PERSON><PERSON><PERSON>", "intermediate": "Intermediate", "advanced": "Advanced", "duration": "{{minutes}} min", "benefits": "Benefits", "instructions": "Instructions", "keyPoints": "Key Points", "startPose": "Start Pose", "nextPose": "Next Pose", "completePose": "Complete Pose", "sessionComplete": "Session Complete!", "accuracy": "Accuracy: {{percentage}}%", "feedback": "<PERSON><PERSON><PERSON>", "improvePosture": "This pose helps improve your posture"}, "progress": {"title": "Your Progress", "overview": "Overview", "thisWeek": "This Week", "thisMonth": "This Month", "totalSessions": "Total Sessions", "totalMinutes": "Total Minutes", "averageScore": "Average Score", "improvement": "Improvement", "streakDays": "Streak Days", "weeklyGoal": "Weekly Goal", "monthlyGoal": "Monthly Goal", "achievements": "Achievements", "badges": "Badges", "shareProgress": "Share Progress", "exportData": "Export Data"}, "profile": {"title": "Profile", "editProfile": "Edit Profile", "personalInfo": "Personal Information", "preferences": "Preferences", "language": "Language", "notifications": "Notifications", "reminderTime": "Reminder Time", "yogaExperience": "Yoga Experience", "focusAreas": "Focus Areas", "family": "Family Members", "addFamilyMember": "Add Family Member", "subscription": "Subscription", "paymentHistory": "Payment History", "support": "Support", "about": "About", "privacy": "Privacy Policy", "terms": "Terms of Service"}, "subscription": {"title": "Choose Your Plan", "currentPlan": "Current Plan", "free": "Free", "plus": "Plus", "pro": "Pro", "premium": "Premium", "monthly": "Monthly", "yearly": "Yearly", "save": "Save {{percentage}}%", "features": {"postureChecks": "{{count}} posture checks/month", "unlimitedChecks": "Unlimited posture checks", "exercises": "{{count}} yoga exercises", "unlimitedExercises": "All yoga exercises", "analytics": "Advanced analytics", "familyTracking": "Family tracking", "aiCoaching": "AI coaching", "wearableIntegration": "Wearable integration", "personalizedPrograms": "Personalized programs", "adFree": "Ad-free experience"}, "subscribe": "Subscribe Now", "upgrade": "Upgrade Plan", "downgrade": "Downgrade Plan", "cancel": "Cancel Subscription", "renews": "Renews on {{date}}", "expires": "Expires on {{date}}", "paymentMethods": "Payment Methods", "upi": "UPI", "paytm": "Paytm", "card": "Credit/Debit Card", "netbanking": "Net Banking"}, "family": {"title": "Family Health", "addMember": "Add Family Member", "memberName": "Name", "relationship": "Relationship", "age": "Age", "relationships": {"spouse": "Spouse", "child": "Child", "parent": "Parent", "sibling": "Sibling", "other": "Other"}, "memberAdded": "Family member added successfully", "memberProgress": "{{name}}'s Progress", "shareWithFamily": "Share with Family", "familyChallenge": "Family Challenge", "weeklyReport": "Weekly Family Report"}, "notifications": {"title": "Notifications", "postureReminder": "Time for your posture check!", "yogaReminder": "Don't forget your daily yoga session", "achievement": "Congratulations! You've earned a new badge", "familyUpdate": "{{name}} completed their yoga session", "tip": "Daily Tip: {{tip}}", "streakReminder": "Keep your {{count}} day streak going!"}, "errors": {"networkError": "Having trouble connecting. Please check your internet! 🌐", "cameraPermission": "We need camera access to check your posture. Could you allow this? 📷", "microphonePermission": "We need microphone access for this feature. Could you allow this? 🎤", "paymentFailed": "Payment didn't go through. Let's try again! 💳", "subscriptionError": "Something went wrong with your subscription. Let's fix this! 💫", "authError": "Having trouble signing you in. Let's try again! 🔐", "invalidEmail": "Could you double-check your email? It doesn't look quite right 📧", "weakPassword": "Your password needs to be stronger - at least 6 characters! 🔒", "passwordMismatch": "The passwords don't match - could you try typing them again? 🔒", "requiredField": "This field needs to be filled out to continue 📝", "somethingWentWrong": "Oops! Something didn't work as expected. Let's try again! 🔄"}, "poses": {"mountainPose": "Mountain Pose", "catCowPose": "Cat-Cow <PERSON>", "childsPose": "Child's Pose", "downwardDog": "Downward Facing Dog", "cobrapose": "Cobra Pose", "bridgePose": "Bridge Pose", "spinalTwist": "Spinal Twist", "neckRolls": "Neck Rolls", "shoulderShrugs": "Shoulder Shrugs", "chestOpener": "Chest Opener", "hipFlexorStretch": "Hip Flexor Stretch", "thoracicExtension": "Thoracic Extension", "wallAngels": "Wall Angels", "doorwayStretch": "Doorway Stretch", "upperTrapStretch": "Upper Trap Stretch"}, "achievements": {"firstSession": "First Session Complete", "weekStreak": "7 Day Streak", "monthStreak": "30 Day Streak", "perfectPosture": "Perfect Posture", "yogaMaster": "Yoga Master", "familyMotivator": "Family Motivator", "consistentPractice": "Consistent Practice", "postureImprover": "Posture Improver"}, "questionnaire": {"exitConfirm": "🤔 Want to take a break?", "exitMessage": "No worries! We'll keep your answers safe. You can come back anytime to continue.", "saveAndExit": "💾 Save & Exit", "complete": "🎉 Assessment Complete!", "completeMessage": "Amazing! Your personalized wellness plan is ready!", "viewRecommendations": "🎯 See My Plan", "initError": "Couldn't start the questionnaire. Let's give it another try! 📝", "submitError": "Couldn't save your answer. Don't worry, let's try again! 💾", "unsupportedQuestionType": "Oops! This question type isn't supported yet 🤔"}}